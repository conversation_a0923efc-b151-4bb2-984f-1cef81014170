@charset "utf-8";

a,
abbr,
acronym,
address,
applet,
article,
aside,
audio,
b,
big,
blockquote,
body,
button,
canvas,
caption,
center,
cite,
code,
dd,
del,
details,
dfn,
div,
dl,
dt,
em,
embed,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
html,
i,
iframe,
img,
input,
ins,
kbd,
label,
legend,
li,
mark,
menu,
nav,
object,
ol,
output,
p,
pre,
q,
ruby,
s,
samp,
section,
select,
small,
span,
strike,
strong,
sub,
summary,
sup,
table,
tbody,
td,
textarea,
tfoot,
th,
thead,
time,
tr,
tt,
u,
ul,
var,
main,
video {
	margin: 0;
	padding: 0;
	border: 0;
	outline: none;
	-webkit-appearance: none;
	border: none;
	box-sizing: border-box;
}

b,
strong {
	font-weight: 600;
}

// .video-js .vjs-progress-control .vjs-play-progress:before {
//     top: -0.443333em;
// }

body,
html {
	//   -webkit-overflow-scrolling: touch;
	position: relative;
	touch-action: manipulation;
	font-weight: 400;
	//   -webkit-tap-highlight-color: transparent;
	-webkit-text-size-adjust: none;
	-webkit-font-smoothing: antialiased;
	font-style: normal;
	-webkit-text-size-adjust: none;
	color: $textColor1;
}

.html_gray {
	-webkit-filter: grayscale(100%);
	-moz-filter: grayscale(100%);
	-ms-filter: grayscale(100%);
	-o-filter: grayscale(100%);
	filter: grayscale(100%);
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
	display: block;
}

ol,
ul {
	list-style: none;
}

blockquote,
q {
	quotes: none;
}

blockquote:after,
blockquote:before,
q:after,
q:before {
	content: "";
	content: none;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

textarea {
	overflow: auto;
	vertical-align: top;
	resize: vertical;
}

[hidden] {
	display: none;
}

a:active,
a:hover {
	outline: 0;
}

img {
	border: 0;
}
img[src=""],
img:not([src]) {
	opacity: 0;
}

legend {
	border: 0;
	padding: 0;
	white-space: normal;
}

button,
input,
select,
textarea {
	outline: none;
	-webkit-appearance: none;
	border: none;
	background: none;
}

button,
input {
	line-height: normal;
}

button,
select {
	text-transform: none;
}

button,
input,
select,
textarea {
	-webkit-appearance: button;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
	border: 0;
	padding: 0;
}

textarea {
	overflow: auto;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

a {
	text-decoration: none;
	color: $textColor5;

	&:hover {
		text-decoration: underline;
	}

	@media (max-width: 768px) {
		&:hover {
			text-decoration: none;
		}
	}
}

.fixScroll {
	overflow: hidden !important;
}

input:-webkit-autofill,
input:-webkit-autofill:active,
input:-webkit-autofill:focus,
input:-webkit-autofill:hover {
	-webkit-box-shadow: 0 0 0 1000px $borderColor6 inset;
}
.is-newRegular {
	input:-webkit-autofill,
	input:-webkit-autofill:active,
	input:-webkit-autofill:focus,
	input:-webkit-autofill:hover {
		-webkit-box-shadow: none;
	}
}

textarea:-webkit-autofill,
textarea:-webkit-autofill:active,
textarea:-webkit-autofill:focus,
textarea:-webkit-autofill:hover {
	-webkit-box-shadow: 0 0 0 1000px $borderColor6 inset;
}

input:focus::-webkit-input-placeholder {
	color: transparent;
}

input:focus:-moz-placeholder {
	color: transparent;
}

input:focus::-moz-placeholder {
	color: transparent;
}

input:focus:-ms-input-placeholder {
	color: transparent;
}

textarea:focus::-webkit-input-placeholder {
	color: transparent;
}

textarea:focus:-moz-placeholder {
	color: transparent;
}

textarea:focus::-moz-placeholder {
	color: transparent;
}

textarea:focus:-ms-input-placeholder {
	color: transparent;
}

input::-webkit-input-placeholder {
	/* WebKit browsers */
	color: $textColor2;
	@include font13;
}

input:-moz-placeholder {
	/* Mozilla Firefox 4 to 18 */
	color: $textColor2;
	@include font13;
}

input::-moz-placeholder {
	/* Mozilla Firefox 19+ */
	color: $textColor2;
	@include font13;
}

input:-ms-input-placeholder {
	/* Internet Explorer 10+ */
	color: $textColor2;
	@include font13;
}

textarea::-webkit-input-placeholder {
	/* WebKit browsers */
	color: $textColor2;
	@include font13;
}

textarea:-moz-placeholder {
	/* Mozilla Firefox 4 to 18 */
	color: $textColor2;
	@include font13;
}

textarea::-moz-placeholder {
	/* Mozilla Firefox 19+ */
	color: $textColor2;
	@include font13;
}

textarea:-ms-input-placeholder {
	/* Internet Explorer 10+ */
	color: $textColor2;
	@include font13;
}

input {
	display: block;
	width: 100%;
	height: 42px;
	@include font13;
	border: 1px solid $borderColor1;
	border-radius: 3px;
	padding: 10px 12px;
	transition: all 0.3s;
	color: $textColor1;

	&:focus {
		border: 1px solid $borderColor4;
	}
}

input[type="checkbox"] {
	appearance: none;
	-webkit-appearance: none;
	display: inline-block;
	width: 18px;
	height: 18px;
	border: none;
	border-radius: 0;
	padding: 0;
	line-height: 1;
	background: none;
	background-color: transparent;
	outline: none;
	position: relative;
	vertical-align: middle;
	font-size: 18px;

	// @media (max-width: 960px) {
	//     width: 20px;
	//     height: 20px;
	//     font-size: 20px;
	// }

	&:before {
		content: "\f043";
		color: $textColor4;
		font-family: "iconfont" !important;
		-webkit-font-smoothing: antialiased;
		cursor: pointer;
		transition: all 0.3s;
	}

	&:hover {
		&:before {
			content: "\f043";
			color: $textColor2;
		}
	}

	&[halfChecked="true"] {
		&::before {
			content: "\e287";
			color: $textColor2;
		}
	}

	&[halfChecked="true"]:hover {
		&::before {
			color: $textColor2;
		}
	}

	&:checked {
		&:before {
			color: $textColor2;
			font-family: "iconfont" !important;
			-webkit-font-smoothing: antialiased;
			content: "\f186";
		}

		&:hover {
			&:before {
				color: $textColor2;
			}
		}
	}

	&:disabled {
		&:before {
			color: $textColor4;
		}

		&:before {
			color: $textColor4;
		}
	}
}

input[type="radio"] {
	appearance: none;
	-webkit-appearance: none;
	display: inline-block;
	width: 18px;
	height: 18px;
	border: none;
	border-radius: 0;
	padding: 0;
	line-height: 1;
	background: none;
	background-color: transparent;
	outline: none;
	position: relative;
	font-size: 18px;
	margin-right: 5px;

	&:before {
		content: "\f051";
		color: $textColor4;
		font-family: "iconfont" !important;
		-webkit-font-smoothing: antialiased;
		cursor: pointer;
	}

	&:hover {
		&:before {
			content: "\f051";
			color: $textColor2;
		}
	}

	&:checked {
		&:before {
			color: $textColor2;
			font-family: "iconfont" !important;
			-webkit-font-smoothing: antialiased;
			content: "\f050";
		}

		&:hover {
			&:before {
				color: $textColor2;
			}
		}
	}

	&:disabled {
		&:before {
			color: $textColor4;
			opacity: 0.8;
		}
	}
}

input[type="radio"]:hover,
input[type="checkbox"]:hover {
	@media (max-width: 1024px) {
		&::before {
			color: $textColor4;
		}
	}
}

input[type="checkbox"]:indeterminate:hover {
	color: $textColor2;
}

select {
	appearance: none;
	-moz-appearance: none;
	-webkit-appearance: none;
	height: 42px;
	width: 100%;
	position: relative;
	color: $textColor1;
	border: 1px solid $borderColor1;
	border-radius: 3px;
	padding: 0 32px 0 12px;
	background-image: url("https://resource.fs.com/mall/generalImg/20241219140745u94akk.svg");
	background-repeat: no-repeat;
	background-position: right 10px center;
	background-size: 14px;
	font-size: 13px;
}

textarea {
	display: block;
	height: 110px;
	width: 100%;
	border-radius: 3px;
	border: 1px solid $borderColor1;
	// transition: all 0.3s;
	color: $textColor1;
	font-size: 13px;
	padding: 8px 12px;
	resize: none;

	&:focus {
		border: 1px solid $borderColor4;
	}
}

input,
select,
textarea {
	&:disabled {
		background-color: $bgColor1;
	}
}

.clearfix:after {
	content: " ";
	display: block;
	clear: both;
	visibility: hidden;
	height: 0;
}

div[id*="trustbadge-container"] ._owyw4l {
	bottom: 290px !important;
}

div[id*="trustbadge-container"] {
	z-index: 200000 !important;

	._12n8yed {
		inset: auto 20px 102px auto !important;
	}
}

// #MyCustomTrustbadge {
// 	position: relative;
// 	// margin-top: 18px;

// 	._1gwv20v {
// 		._lk7o4h {
// 			position: relative !important;
// 			border: 1px solid $textColor4 !important;
// 			border-radius: 3px !important;
// 			padding: 11px 16px !important;
// 			box-sizing: border-box !important;

// 			._thsmae {
// 				width: 100% !important;
// 				align-items: center !important;

// 				._1qiwh36 {
// 					width: 58px !important;
// 					height: 58px !important;
// 					padding: 0 !important;
// 					margin: 0 !important;
// 					margin-right: 12px !important;

// 					._upwhbk {
// 						display: block !important;
// 						margin: auto !important;
// 						width: 58px !important;
// 						height: 58px !important;
// 					}
// 				}

// 				._argvb9 {
// 					flex: 1 !important;
// 					display: flex !important;
// 					flex-direction: column !important;
// 					justify-content: center !important;
// 					padding: 12px 0 0 0 !important;
// 					align-items: center !important;
// 					height: 58px !important;

// 					._s7xc8z {
// 						color: $textColor2 !important;
// 						font-size: 12px !important;
// 						line-height: 18px !important;
// 						font-display: swap !important;
// 						padding: 0 !important;
// 						margin-top: 2px !important;
// 					}

// 					._8pqgf9 {
// 						color: $textColor2 !important;
// 						font-display: swap !important;
// 						font-size: 13px !important;
// 						margin-top: 4px !important;
// 						height: 13px !important;
// 						line-height: 13px !important;
// 						margin-bottom: -2px !important;

// 						> span {
// 							display: inline-block !important;
// 							height: 13px !important;
// 							color: #646466 !important;
// 							font-display: swap !important;
// 							line-height: 13px !important;
// 							font-size: 13px !important;
// 						}
// 					}
// 				}
// 			}

// 			._zbxp0s {
// 				display: none !important;
// 			}

// 			._1iu1jow {
// 				display: none !important;
// 			}

// 			._qcra45 {
// 				padding: 0 !important;
// 				top: 6px !important;
// 				font-size: 13px !important;
// 				color: #19191a !important;
// 				white-space: nowrap !important;
// 				margin: 0 !important;
// 				position: absolute !important;
// 				text-align: center !important;
// 				width: calc(100% - 32px) !important;
// 				padding-left: 70px !important;
// 				left: 16px !important;
// 				text-align: center !important;
// 				font-display: swap !important;
// 			}
// 		}
// 	}

// 	&.MyCustomTrustbadge-de {
// 		._1gwv20v {
// 			._lk7o4h {
// 				width: 182px !important;
// 			}
// 		}
// 	}

// 	&.MyCustomTrustbadge-de-en {
// 		._1gwv20v {
// 			._lk7o4h {
// 				width: 204px !important;
// 			}
// 		}
// 	}

// 	&.MyCustomTrustbadge-fr {
// 		._1gwv20v {
// 			._lk7o4h {
// 				width: 223px !important;
// 			}
// 		}
// 	}

// 	&.MyCustomTrustbadge-es {
// 		._1gwv20v {
// 			._lk7o4h {
// 				width: 254px !important;
// 			}
// 		}
// 	}

// 	&.MyCustomTrustbadge-mx {
// 		._1gwv20v {
// 			._lk7o4h {
// 				width: 254px !important;
// 			}
// 		}
// 	}

// 	&.MyCustomTrustbadge-it {
// 		._1gwv20v {
// 			._lk7o4h {
// 				width: 233px !important;
// 			}
// 		}
// 	}
// }

.fsLiveChat {
	position: fixed;
	width: 392px;
	height: 632px;
	bottom: 32px;
	right: -408px;
	z-index: 999;
	transition: right 0.3s ease-in-out;
	&.show {
		right: 68px;
		opacity: 1;
	}
  @media (min-width: 768px) and (max-width: 1024px){
      &.show {
        right: 72px;
        bottom: 24px;
      }
  }
	// 媒体查询移动端
	@media (max-width: 767px) {
		width: 100%;
		height: 100%;
		bottom: 0;
		right: -100%;
		&.show {
			right: 0;
			z-index: 99999;
		}
	}
	@media (max-height: 700px) {
		bottom: 0;
		height: 100%;
	}
}

//animate 动画
@keyframes LiveChatLeftToRight {
	0% {
		left: 0;
	}

	50% {
		left: 47px;
	}

	100% {
		left: 0;
	}
}

@keyframes LiveChatRightToLeft {
	0% {
		right: 0;
	}

	50% {
		right: 47px;
	}

	100% {
		right: 0;
	}
}

@-webkit-keyframes animation-skeleton-wave {
	0% {
		-webkit-transform: translateX(-100%);
		-moz-transform: translateX(-100%);
		-ms-transform: translateX(-100%);
		transform: translateX(-100%);
	}

	50% {
		-webkit-transform: translateX(100%);
		-moz-transform: translateX(100%);
		-ms-transform: translateX(100%);
		transform: translateX(100%);
	}

	100% {
		-webkit-transform: translateX(100%);
		-moz-transform: translateX(100%);
		-ms-transform: translateX(100%);
		transform: translateX(100%);
	}
}

@keyframes animation-skeleton-wave {
	0% {
		-webkit-transform: translateX(-100%);
		-moz-transform: translateX(-100%);
		-ms-transform: translateX(-100%);
		transform: translateX(-100%);
	}

	50% {
		-webkit-transform: translateX(100%);
		-moz-transform: translateX(100%);
		-ms-transform: translateX(100%);
		transform: translateX(100%);
	}

	100% {
		-webkit-transform: translateX(100%);
		-moz-transform: translateX(100%);
		-ms-transform: translateX(100%);
		transform: translateX(100%);
	}
}

// *** 全局设置组件库 输入框高度 ****
.fs-input__inner {
	height: 20px;
}

.loading-directive-relative {
	position: relative;
}
h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: 600;
}
.grecaptcha-badge {
	display: none !important;
}

.cartAnimation {
	animation: cartScale 0.5s ease-in-out;
}
//实现购物车弹簧动画
@keyframes cartScale {
	0% {
		transform: scale(1);
	}
	20% {
		transform: scale(1.2);
	}
	60% {
		transform: scale(0.8);
	}
	80% {
		transform: scale(1.1);
	}
	100% {
		transform: scale(1);
	}
}
