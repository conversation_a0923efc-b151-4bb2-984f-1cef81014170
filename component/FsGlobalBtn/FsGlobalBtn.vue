<template>
	<ClientOnly>
		<div>
			<Teleport to="body">
				<div class="fs_global_btn_box" :class="{ fs_global_btn_box_scroll: isScroll }" :style="{ bottom: globalBtnBottom }">
					<div class="back_top_btn btn_box" :class="{ active: showBackTopBtn }" @click.stop="backTop">
						<span class="iconfont iconfont_top">&#xe630;</span>
					</div>
					<div class="service_btn_wrap">
						<div class="service_btn_box" :class="{ service_btn_hide: showFoldBtn }">
							<div id="bdtj_lxwm" class="livechat_btn btn_box btn_box_line" @click.stop="liveChatClick">
								<template v-if="!unreadMsgStatus">
									<span class="iconfont iconfont_chat">&#xe648;</span>
								</template>
								<img v-else src="https://resource.fs.com/mall/generalImg/20250704142455q0c82q.svg" alt="" />
							</div>
							<div class="consult_btn btn_box btn_box_line" @click.stop="showContact">
								<span class="iconfont iconfont_consult">&#xe728;</span>
							</div>
						</div>
						<div class="fold_btn btn_box" :class="{ btn_folded: showFoldBtn }" tabindex="0" @click="toogleFold">
							<span class="iconfont iconfont_fold">&#xe708;</span>
						</div>
					</div>
				</div>

				<div class="fs_global_btn_box_padMobile" :class="{ fs_global_btn_box_padMobile_cookie: showCookieTip }" :style="{ bottom: globalBtnBottom }">
					<div id="bdtj_lxwm" class="padMobile_btn livechat_btn" @click.stop="liveChatClick">
						<template v-if="!unreadMsgStatus">
							<span class="iconfont iconfont_chat">&#xe648;</span>
						</template>
						<img v-else src="https://resource.fs.com/mall/generalImg/20250704142455q0c82q.svg" alt="" />
					</div>
					<div class="padMobile_btn consult_btn" @click.stop="showContact">
						<span class="iconfont iconfont_consult">&#xe728;</span>
					</div>
				</div>
			</Teleport>
			<ContactSales v-model="showContactSales" :bottom="globalBtnBottom" />
		</div>
	</ClientOnly>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import ContactSales from "@/popup/ContactSales/index.vue";
const headerStore = useHeaderStore();
const bdRequest = useBdRequest();
const showBackTopBtn = ref(false);
const showContactSales = defineModel({ type: Boolean, default: false });
const route = useRoute();
const showFoldBtn = ref(false);
const deviceStore = useDeviceStore();
const { screenWidth } = storeToRefs(deviceStore);
const liveChatStore = useLiveChatStore();
const unreadMsgStatus = computed(() => {
	return liveChatStore.unreadMsgStatus;
});
console.log("liveChatStore", liveChatStore);

const isScroll = ref(false);
let timer: number | null = null;
const { showCookieTipNew, showCookieTip, showCookieTipCommon } = storeToRefs(headerStore);
const cookieTipHeight = ref(0);
const gaPoint = function () {
	usePoint({ eventAction: "floating_button", eventLabel: "Live Chat" });
};
function updateCookieTipHeight() {
  console.count()
  console.log('cookie status', showCookieTipNew.value , showCookieTipCommon.value ,showCookieTip.value)
  let timer = null
  const el = 
  timer = setTimeout(() => {
		const el = document.querySelector(".cookie_box") as HTMLElement;
		console.log(el, "elel");
		cookieTipHeight.value = el ? el.offsetHeight : 0;
	}, 100);
}

const globalBtnBottom = computed(() => {
	if (showCookieTipNew.value || (showCookieTipCommon.value && showCookieTip.value)) {
		return screenWidth.value <= 1024 ? `${cookieTipHeight.value + 16}px` : `${cookieTipHeight.value + 48}px`;
	} else {
		return `40px`;
	}
});
function showContact() {
	hideLiveChat();
	showContactSales.value = true;
	bdRequest([
		{
			logidUrl: location.href,
			newType: 5
		}
	]);
}

const scrollToElement = useScrollToElement();

const { showLiveChat, hideLiveChat } = useLiveChat();

function backTop() {
	scrollToElement({ ele: document.body });
}

const liveChatClick = () => {
	gaPoint();
	showContactSales.value = false;
	showLiveChat();
	bdRequest([
		{
			logidUrl: location.href,
			newType: 1
		}
	]);
};

const onScroll = () => {
	const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
	const clientHeight = window.screen.availHeight;
	const page = /\/products\/\d+\.html/.test(location.pathname) ? 1 : 2;
	if (scrollTop >= clientHeight * page) {
		showBackTopBtn.value = true;
	} else {
		showBackTopBtn.value = false;
	}
	isScroll.value = true;
	if (timer) {
		clearTimeout(timer);
		timer = null;
	}
	timer = window.setTimeout(() => {
		isScroll.value = false;
	}, 200);
};

function toogleFold() {
	showFoldBtn.value = !showFoldBtn.value;
	localStorage.setItem("suspendFold", `${showFoldBtn.value}`);
}

useEventListener("window", "scroll", onScroll);

onMounted(async () => {
	updateCookieTipHeight();
	window.addEventListener("resize", updateCookieTipHeight);
	await nextTick();
	showFoldBtn.value = localStorage.getItem("suspendFold") === "true";

	const observer = new MutationObserver(() => {
		const chatEl = document.querySelector("iframe.fsLiveChat") as HTMLElement;
		if (chatEl) {
			// 解析 globalBtnBottom 的数值部分
			const bottomNum = Number(globalBtnBottom.value.replace("px", "")) || 0;
			if (screenWidth.value >= 768) {
				chatEl.style.bottom = bottomNum - 16 + "px";
			} else {
				chatEl.style.bottom = 0 + "px";
			}
			console.log("chatEl.style.bottom==", chatEl.style.bottom, "globalBtnBottom==", globalBtnBottom.value);
			observer.disconnect();
		}
	});
	observer.observe(document.body, { childList: true, subtree: true });
});
watch([showCookieTipCommon, showCookieTip], updateCookieTipHeight);

// 如果 globalBtnBottom 变化时也要同步 livechat 的 bottom，可以 watch
watch(globalBtnBottom, async val => {
	await nextTick();
	const chatEl = document.querySelector("iframe.fsLiveChat") as HTMLElement;
	if (chatEl) {
		const bottomNum = Number(val.replace("px", "")) || 0;
		if (screenWidth.value >= 768) {
			chatEl.style.bottom = bottomNum - 16 + "px";
		} else {
			chatEl.style.bottom = 0 + "px";
		}

		console.log("chatEl==", chatEl.style.bottom, bottomNum);
	}
});
</script>

<style lang="scss" scoped>
.fs_global_btn_box {
	position: fixed;
	display: flex;
	flex-direction: column;
	right: 20px;
	bottom: 100px;
	z-index: 98;

	@include padMobile() {
		display: none;
		right: 16px;
	}

	&.fs_global_btn_box_scroll {
		@include padMobile() {
			opacity: 0.5;
		}
	}
}

.service_btn_wrap {
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: center;
	// box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
	// border-radius: 3px;
}

.service_btn_box {
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: center;
	gap: 8px;
	// height: 96px;
	transition: all 0.3s;
	// overflow: hidden;

	&.service_btn_hide {
		@include padMobile() {
			height: 0;
		}
	}
}

.btn_box {
	width: 48px;
	height: 48px;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #ffffff;
	transition: all 0.3s;
	cursor: pointer;
	user-select: none;
	position: relative;

	&.back_top_btn {
		opacity: 0;
		margin-bottom: 16px;
		border-radius: 50%;
		// box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
		box-shadow: 0px 1.71px 6.86px 0px rgba(0, 0, 0, 0.1);
		transition: all 0.3s;
		cursor: pointer;

		&.active {
			opacity: 1;
		}
	}

	&.livechat_btn {
		border-radius: 50%;
		box-shadow: 0px 1.71px 6.86px 0px rgba(0, 0, 0, 0.1);

		img {
			width: 24px;
		}
	}

	&.consult_btn {
		border-radius: 50%;
		background-color: #707070;
		box-shadow: 0px 1.71px 6.86px 0px rgba(0, 0, 0, 0.1);

		@include padMobile() {
			background-color: #fff;
			border-radius: 0;
		}

		.iconfont {
			color: #fff;

			&:hover {
				color: #fff;
			}

			@include padMobile() {
				color: $textColor2;

				&:hover {
					color: $textColor1;
				}
			}
		}
	}

	&.btn_box_line {
		@include padMobile() {
			&::after {
				content: "";
				position: absolute;
				bottom: 0;
				left: 7px;
				right: 7px;
				background-color: #e5e5e5;
				height: 1px;
				z-index: 2;
			}
		}
	}

	&.fold_btn {
		display: none;

		@include padMobile() {
			display: flex;
			border-radius: 0 0 4px 4px;

			&.btn_folded {
				border-radius: 4px;
			}
		}
	}

	&.btn_folded {
		.iconfont_fold {
			transform: rotateZ(180deg);
		}
	}

	.iconfont {
		font-size: 24px;
		display: block;
		width: 24px;
		height: 24px;
		line-height: 24px;
		color: $textColor2;
		transition: all 0.3s;

		@include padMobile() {
			font-size: 21px;
			display: block;
			width: 21px;
			height: 21px;
			line-height: 21px;

			&:hover {
				color: $textColor2;
			}
		}

		&:hover {
			color: $textColor1;
		}

		&.iconfont_fold {
			font-size: 16px;
			display: block;
			width: 16px;
			height: 16px;
			line-height: 16px;
		}
	}
}

.fs_global_btn_box_padMobile {
	position: fixed;
	display: none;
	flex-direction: column;
	right: 10px;
	bottom: 16px;
	z-index: 98;
	display: none;

	@include padMobile() {
		display: flex;
		right: 20px;
	}

	.padMobile_btn {
		width: 48px;
		height: 48px;
		border-radius: 30px;
		display: flex;
		justify-content: center;
		align-items: center;

		img {
			width: 24px;
		}

		&.consult_btn {
			background: #707070;
			color: #fff;
			margin-top: 8px;
			.iconfont {
				width: 24px;
				height: 24px;
				font-size: 24px;
			}
		}

		&.livechat_btn {
			box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
			background: #ffffff;
			color: #707070;
			.iconfont {
				width: 24px;
				height: 24px;
				font-size: 24px;
			}
		}
	}
}
</style>
